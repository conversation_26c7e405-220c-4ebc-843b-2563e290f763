import { createSlice } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';

const initialState = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false,
    hide: false,
  },
  device: 'desktop',
  size: Cookies.get('size') || 'default',
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      if (state.sidebar.hide) {
        return;
      }
      state.sidebar.opened = !state.sidebar.opened;
      state.sidebar.withoutAnimation = false;
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', 1);
      } else {
        Cookies.set('sidebarStatus', 0);
      }
    },
    closeSidebar: (state, action) => {
      const withoutAnimation = action.payload;
      Cookies.set('sidebarStatus', 0);
      state.sidebar.opened = false;
      state.sidebar.withoutAnimation = withoutAnimation;
    },
    toggleDevice: (state, action) => {
      state.device = action.payload;
    },
    setSize: (state, action) => {
      state.size = action.payload;
      Cookies.set('size', action.payload);
    },
    setSidebarHide: (state, action) => {
      state.sidebar.hide = action.payload;
    },
  },
});

export const {
  toggleSidebar,
  closeSidebar,
  toggleDevice,
  setSize,
  setSidebarHide,
} = appSlice.actions;

export default appSlice.reducer;
