import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  visitedViews: [],
  cachedViews: [],
};

const tagsViewSlice = createSlice({
  name: 'tagsView',
  initialState,
  reducers: {
    addView: (state, action) => {
      const view = action.payload;
      if (state.visitedViews.some(v => v.path === view.path)) return;
      state.visitedViews.push({
        name: view.name,
        path: view.path,
        title: view.meta?.title || 'no-name',
        affix: view.meta?.affix || false,
      });
    },
    addCachedView: (state, action) => {
      const view = action.payload;
      if (state.cachedViews.includes(view.name)) return;
      if (!view.meta?.noCache) {
        state.cachedViews.push(view.name);
      }
    },
    delView: (state, action) => {
      const targetView = action.payload;
      state.visitedViews = state.visitedViews.filter(v => v.path !== targetView.path);
      state.cachedViews = state.cachedViews.filter(name => name !== targetView.name);
    },
    delAllViews: (state) => {
      const affixTags = state.visitedViews.filter(tag => tag.affix);
      state.visitedViews = affixTags;
      state.cachedViews = [];
    },
    delOthersViews: (state, action) => {
      const targetView = action.payload;
      state.visitedViews = state.visitedViews.filter(v => v.affix || v.path === targetView.path);
      state.cachedViews = state.cachedViews.filter(name => name === targetView.name);
    },
  },
});

export const {
  addView,
  addCachedView,
  delView,
  delAllViews,
  delOthersViews,
} = tagsViewSlice.actions;

export default tagsViewSlice.reducer;
