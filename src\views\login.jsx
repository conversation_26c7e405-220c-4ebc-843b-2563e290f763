import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Form, Input, Button, Checkbox, message, Row, Col } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import Cookies from 'js-cookie';
import { loginAsync } from '@/store/slices/userSlice';
import { encrypt, decrypt } from '@/utils/rsaUtil';
import * as loginApi from '@/api/loginApi';
import '@/assets/styles/login.scss';

const Login = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  
  const { loading } = useSelector((state) => state.user);
  
  const [codeUrl, setCodeUrl] = useState('');
  const [isCaptchaOn, setIsCaptchaOn] = useState(false);
  const [register] = useState(false);
  
  const redirect = location.state?.from?.pathname || '/';

  useEffect(() => {
    getCookie();
    getCode();
  }, []);

  const getCookie = () => {
    const username = Cookies.get('username');
    const password = Cookies.get('password');
    const rememberMe = Cookies.get('rememberMe');
    
    if (username && password) {
      form.setFieldsValue({
        username,
        password: decrypt(password),
        rememberMe: rememberMe === 'true',
      });
    }
  };

  const getCode = async () => {
    try {
      const res = await loginApi.getCodeImg();
      setIsCaptchaOn(res.captchaOnOff === undefined ? true : res.captchaOnOff);
      if (res.captchaOnOff) {
        setCodeUrl('data:image/gif;base64,' + res.img);
        form.setFieldValue('uuid', res.uuid);
      }
    } catch (error) {
      console.error('获取验证码失败:', error);
    }
  };

  const handleLogin = async (values) => {
    try {
      // 处理记住密码
      if (values.rememberMe) {
        Cookies.set('username', values.username, { expires: 30 });
        Cookies.set('password', encrypt(values.password), { expires: 30 });
        Cookies.set('rememberMe', values.rememberMe, { expires: 30 });
      } else {
        Cookies.remove('username');
        Cookies.remove('password');
        Cookies.remove('rememberMe');
      }

      // 登录
      await dispatch(loginAsync(values)).unwrap();
      message.success('登录成功');
      navigate(redirect);
    } catch (error) {
      message.error(error || '登录失败');
      if (isCaptchaOn) {
        getCode();
      }
    }
  };

  return (
    <div className="login">
      <Form
        form={form}
        name="loginForm"
        className="login-form"
        onFinish={handleLogin}
        autoComplete="off"
      >
        <h2 className="title">欧迪恩数据采集系统</h2>
        
        <Form.Item
          name="username"
          rules={[{ required: true, message: '请输入您的账号' }]}
        >
          <Input
            prefix={<UserOutlined className="input-icon" />}
            placeholder="账号"
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[{ required: true, message: '请输入您的密码' }]}
        >
          <Input.Password
            prefix={<LockOutlined className="input-icon" />}
            placeholder="密码"
            size="large"
          />
        </Form.Item>

        {isCaptchaOn && (
          <Form.Item>
            <Row gutter={8}>
              <Col span={15}>
                <Form.Item
                  name="code"
                  noStyle
                  rules={[{ required: true, message: '请输入验证码' }]}
                >
                  <Input
                    prefix={<SafetyOutlined className="input-icon" />}
                    placeholder="验证码"
                    size="large"
                  />
                </Form.Item>
              </Col>
              <Col span={9}>
                <div className="login-code">
                  <img
                    src={codeUrl}
                    onClick={getCode}
                    className="login-code-img"
                    alt="验证码"
                  />
                </div>
              </Col>
            </Row>
          </Form.Item>
        )}

        <Form.Item name="rememberMe" valuePropName="checked">
          <Checkbox>记住密码</Checkbox>
        </Form.Item>

        <Form.Item name="uuid" hidden>
          <Input />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            style={{ width: '100%' }}
          >
            {loading ? '登 录 中...' : '登 录'}
          </Button>
          {register && (
            <div style={{ float: 'right', marginTop: '10px' }}>
              <a href="/register">立即注册</a>
            </div>
          )}
        </Form.Item>
      </Form>

      <div className="el-login-footer">
        <span>Copyright © 2025 </span>
        <a
          href="https://baike.baidu.com/item/%E8%8B%8F%E5%B7%9E%E6%99%AE%E4%B8%AD%E6%99%BA%E8%83%BD%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8/2542667"
          target="_blank"
          rel="noopener noreferrer"
        >
          苏州普中智能科技有限公司
        </a>
        <span> 电话号码:400-086-9986</span>
      </div>
    </div>
  );
};

export default Login;
