import React from 'react';
import { Card, Table, Button, Space } from 'antd';

const LineConfig = () => {
  const columns = [
    {
      title: '产线名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link">编辑</Button>
          <Button type="link" danger>删除</Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      name: '产线1',
      status: '运行中',
    },
    {
      key: '2',
      name: '产线2',
      status: '停止',
    },
  ];

  return (
    <Card title="产线配置" extra={<Button type="primary">新增产线</Button>}>
      <Table columns={columns} dataSource={data} />
    </Card>
  );
};

export default LineConfig;
