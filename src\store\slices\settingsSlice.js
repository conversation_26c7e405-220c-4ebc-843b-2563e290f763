import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  theme: '#409EFF',
  sideTheme: 'theme-dark',
  showSettings: false,
  topNav: false,
  tagsView: true,
  fixedHeader: false,
  sidebarLogo: true,
  dynamicTitle: false,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    changeSetting: (state, action) => {
      const { key, value } = action.payload;
      if (state.hasOwnProperty(key)) {
        state[key] = value;
      }
    },
  },
});

export const { changeSetting } = settingsSlice.actions;

export default settingsSlice.reducer;
