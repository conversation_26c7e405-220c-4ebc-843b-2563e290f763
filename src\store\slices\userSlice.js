import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import * as loginApi from '@/api/loginApi';
import { getToken, setToken as saveToken, removeToken } from '@/utils/token';
import defAva from '@/assets/images/profile.jpg';

// 异步actions
export const loginAsync = createAsyncThunk(
  'user/login',
  async ({ username, password, code, uuid }, { rejectWithValue }) => {
    try {
      const res = await loginApi.login(username.trim(), password, code, uuid);
      saveToken(res.token);
      return res;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const getInfoAsync = createAsyncThunk(
  'user/getInfo',
  async (_, { rejectWithValue }) => {
    try {
      const res = await loginApi.getLoginUserInfo();
      return res;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const logoutAsync = createAsyncThunk(
  'user/logout',
  async (_, { rejectWithValue }) => {
    try {
      await loginApi.logout();
      removeToken();
      return {};
    } catch (error) {
      removeToken();
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  token: getToken(),
  name: '',
  avatar: '',
  role: '',
  permissions: [],
  dictTypes: {},
  loading: false,
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setToken: (state, action) => {
      state.token = action.payload;
    },
    setName: (state, action) => {
      state.name = action.payload;
    },
    setAvatar: (state, action) => {
      state.avatar = action.payload;
    },
    setRole: (state, action) => {
      state.role = action.payload;
    },
    setPermissions: (state, action) => {
      state.permissions = action.payload;
    },
    setDictTypes: (state, action) => {
      state.dictTypes = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetUser: (state) => {
      state.token = '';
      state.name = '';
      state.avatar = '';
      state.role = '';
      state.permissions = [];
      state.dictTypes = {};
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 登录
      .addCase(loginAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.token;
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 获取用户信息
      .addCase(getInfoAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getInfoAsync.fulfilled, (state, action) => {
        state.loading = false;
        const { user, permissions, dictTypes } = action.payload;
        state.name = user.userName;
        state.avatar = user.avatar === '' ? defAva : user.avatar;
        state.role = user.role;
        state.permissions = permissions;
        state.dictTypes = dictTypes;
      })
      .addCase(getInfoAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 登出
      .addCase(logoutAsync.pending, (state) => {
        state.loading = true;
      })
      .addCase(logoutAsync.fulfilled, (state) => {
        state.loading = false;
        state.token = '';
        state.name = '';
        state.avatar = '';
        state.role = '';
        state.permissions = [];
        state.dictTypes = {};
      })
      .addCase(logoutAsync.rejected, (state) => {
        state.loading = false;
        state.token = '';
        state.name = '';
        state.avatar = '';
        state.role = '';
        state.permissions = [];
        state.dictTypes = {};
      });
  },
});

export const {
  setToken,
  setName,
  setAvatar,
  setRole,
  setPermissions,
  setDictTypes,
  clearError,
  resetUser,
} = userSlice.actions;

export default userSlice.reducer;
