import { configureStore } from '@reduxjs/toolkit';
import appSlice from './slices/appSlice';
import userSlice from './slices/userSlice';
import tagsViewSlice from './slices/tagsViewSlice';
import permissionSlice from './slices/permissionSlice';
import settingsSlice from './slices/settingsSlice';

const store = configureStore({
  reducer: {
    app: appSlice,
    user: userSlice,
    tagsView: tagsViewSlice,
    permission: permissionSlice,
    settings: settingsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export default store;
