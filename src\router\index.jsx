import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Spin } from 'antd';

// 懒加载组件
const Layout = lazy(() => import('@/layout'));
const Login = lazy(() => import('@/views/login'));
const Register = lazy(() => import('@/views/register'));
const OdeLoading = lazy(() => import('@/views/odeLoading'));
const Index = lazy(() => import('@/views/index'));
const Error404 = lazy(() => import('@/views/error/404'));
const Error401 = lazy(() => import('@/views/error/401'));
const Redirect = lazy(() => import('@/views/redirect'));

// 工厂相关页面
const LineConfig = lazy(() => import('@/views/factory/LineList/Config'));
const ProcessConfig = lazy(() => import('@/views/factory/ProcessList/Config'));
const CaijiListConfig = lazy(() => import('@/views/factory/CaijiList/Config'));

// 用户相关页面
const UserProfile = lazy(() => import('@/views/system/user/profile'));

// 加载中组件
const LoadingComponent = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh' 
  }}>
    <Spin size="large" />
  </div>
);

// 路由配置
const AppRouter = () => {
  return (
    <Suspense fallback={<LoadingComponent />}>
      <Routes>
        {/* 重定向路由 */}
        <Route path="/redirect/*" element={<Redirect />} />
        
        {/* 公共路由 */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/odeLoading" element={<OdeLoading />} />
        <Route path="/401" element={<Error401 />} />
        
        {/* 主布局路由 */}
        <Route path="/" element={<Layout />}>
          <Route index element={<Navigate to="/index" replace />} />
          <Route path="index" element={<Index />} />
          
          {/* 工厂配置路由 */}
          <Route path="factory/lineCofig" element={<LineConfig />} />
          <Route path="factory/ProcessConfig" element={<ProcessConfig />} />
          <Route path="factory/CaijiListConfig" element={<CaijiListConfig />} />
          
          {/* 用户相关路由 */}
          <Route path="user/profile" element={<UserProfile />} />
        </Route>
        
        {/* 404 路由 - 必须放在最后 */}
        <Route path="*" element={<Error404 />} />
      </Routes>
    </Suspense>
  );
};

export default AppRouter;
