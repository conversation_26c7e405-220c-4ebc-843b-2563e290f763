{"name": "agileboot-react", "version": "1.8.0", "description": "欧迪恩数据采集系统 - React版本", "author": "valarchie", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "fix": "eslint src/**/*.* --fix"}, "repository": {"type": "git", "url": "https://github.com/valarchie/AgileBoot-Front-End"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@reduxjs/toolkit": "^1.9.7", "antd": "^5.12.8", "axios": "^1.6.2", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "fuse.js": "^7.0.0", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "nprogress": "^0.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-cropper": "^2.3.3"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.55.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "sass": "^1.69.5", "vite": "^5.0.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1"}}