import React from 'react';
import { Card, Descriptions, Avatar, Button } from 'antd';
import { UserOutlined } from '@ant-design/icons';

const UserProfile = () => {
  return (
    <Card title="个人中心" extra={<Button type="primary">编辑资料</Button>}>
      <div style={{ display: 'flex', marginBottom: 24 }}>
        <Avatar size={64} icon={<UserOutlined />} />
        <div style={{ marginLeft: 16 }}>
          <h3>用户名</h3>
          <p>这是用户的个人简介</p>
        </div>
      </div>
      
      <Descriptions title="基本信息" bordered>
        <Descriptions.Item label="用户名">admin</Descriptions.Item>
        <Descriptions.Item label="邮箱"><EMAIL></Descriptions.Item>
        <Descriptions.Item label="手机号">138****8888</Descriptions.Item>
        <Descriptions.Item label="部门">技术部</Descriptions.Item>
        <Descriptions.Item label="职位">系统管理员</Descriptions.Item>
        <Descriptions.Item label="注册时间">2025-01-01</Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default UserProfile;
