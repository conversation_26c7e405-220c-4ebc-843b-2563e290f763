<template>
    <div class="pdf">
      <div class="show" id="full">
        <!-- <a-input placeholder="请扫码" v-model="code" @keyup.enter="enter()"></a-input> -->
        <!-- <a-alert type="error" showIcon message="请输入code!" v-if="infoShow"></a-alert> -->
        <pdf ref="pdf" :src="pdfUrl" v-show="isFullscreen"></pdf>
      </div>
    </div>
  </template>

<script>
import axios from 'axios'
import { getToken } from '@/utils/token';
import pdf from 'vue-pdf'
export default {
  name: 'pdf_preview',
  components: {
    pdf,
  },
  data() {
    return {
      pdfUrl: '',
      code: '',
      isFullscreen: false,
      infoShow: false,
    }
  },
  mounted() {
    //调用监听事件
    // console.log(this.$route)
    this.code = this.$route.query.code;
    this.screenFull()
    this.enter();
  },
  created() {
    
  },
  methods: {
    enter() {
      if (this.code) {
        // console.log(this.code)
        this.getPDF(this.code)
        //全屏效果
        let elem = document.getElementById('full')
        this.requestFullScreen(elem)
      } else {
        this.infoShow = true
      }
    },
    screenFull() {
      //监听f11事件
      window.addEventListener('keydown', this.KeyDown, true)
      document.addEventListener('fullscreenchange', this.fullscreenchange)
      document.addEventListener('mozfullscreenchange', this.fullscreenchange)
      document.addEventListener('webkitfullscreenchange', this.fullscreenchange)
      document.addEventListener('msfullscreenchange', this.fullscreenchange)
    },
    fullscreenchange() {
      this.isFullscreen = true
    },
    KeyDown(event) {
      if (event.keyCode === 122) {
        event.returnValue = false
        this.isScreenFull() //触发全屏的方法
      }
      if (event.keyCode === 13) {
        // if (this.code) {
        //   this.infoShow = false
        // } else {
        //   this.infoShow = true
        // }
        // console.log(this.infoShow)
      }
    },
    requestFullScreen(element) {
      if (element.requestFullscreen) {
        element.requestFullscreen()
      }
      //FireFox
      else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen()
      }
      //Chrome等
      else if (element.webkitRequestFullScreen) {
        element.webkitRequestFullScreen()
      }
      //IE11
      else if (element.msRequestFullscreen) {
        element.msRequestFullscreen()
      }
    },
    getPDF(url) {
      let that =this
    //   let apiBaseUrl = window._CONFIG['domianURL'] || "/base-boot";
      var xhr = new XMLHttpRequest(); // 用这种原生请求下载后端返回的二进制流打开就不会出现空白
      xhr.open(
        "get",
        url,true
      );
      xhr.setRequestHeader('Authorization','Bearer ' + getToken() )
      xhr.responseType = "blob";
      xhr.onload = function() {
        that.pdfUrl = window.URL.createObjectURL(new Blob([this.response], { type: 'application/pdf' }));
        // console.log(that.pdfUrl);
      };
      xhr.send();
    },
  },
}
</script>

<style lang="scss" scoped>
.pdf {
  padding: 20px;
  .show {
    overflow: auto;
    margin: auto;
    max-width: 100vw;
    height: 100vh;
    background: #fff;
    // max-height: 530px;
  }
}
.ant-alert {
  width: 9%;
  margin: 0 auto;
}
::v-deep .ant-alert-icon {
  top: 7.5px;
}
</style>