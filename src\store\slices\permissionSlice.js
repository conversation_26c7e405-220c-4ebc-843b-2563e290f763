import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { constantRoutes } from '@/router/index.jsx';

// 异步获取动态路由
export const generateRoutesAsync = createAsyncThunk(
  'permission/generateRoutes',
  async (permissions, { rejectWithValue }) => {
    try {
      // 这里应该根据权限过滤路由
      // 暂时返回所有常量路由
      return constantRoutes;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  routes: [],
  addRoutes: [],
  loading: false,
  error: null,
};

const permissionSlice = createSlice({
  name: 'permission',
  initialState,
  reducers: {
    setRoutes: (state, action) => {
      state.addRoutes = action.payload;
      state.routes = constantRoutes.concat(action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(generateRoutesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateRoutesAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.addRoutes = action.payload;
        state.routes = constantRoutes.concat(action.payload);
      })
      .addCase(generateRoutesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setRoutes } = permissionSlice.actions;

export default permissionSlice.reducer;
