import React from 'react';
import { Card, Table, Button, Space } from 'antd';

const CaijiListConfig = () => {
  const columns = [
    {
      title: '采集项目',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '采集频率',
      dataIndex: 'frequency',
      key: 'frequency',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link">编辑</Button>
          <Button type="link" danger>删除</Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      name: '温度采集',
      frequency: '每分钟',
      status: '运行中',
    },
    {
      key: '2',
      name: '压力采集',
      frequency: '每5分钟',
      status: '停止',
    },
  ];

  return (
    <Card title="采集项目配置" extra={<Button type="primary">新增采集项目</Button>}>
      <Table columns={columns} dataSource={data} />
    </Card>
  );
};

export default CaijiListConfig;
