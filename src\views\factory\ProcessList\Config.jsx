import React from 'react';
import { Card, Table, Button, Space } from 'antd';

const ProcessConfig = () => {
  const columns = [
    {
      title: '工序名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '工序类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link">编辑</Button>
          <Button type="link" danger>删除</Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      name: '工序A',
      type: '加工',
      status: '启用',
    },
    {
      key: '2',
      name: '工序B',
      type: '检测',
      status: '禁用',
    },
  ];

  return (
    <Card title="工序配置" extra={<Button type="primary">新增工序</Button>}>
      <Table columns={columns} dataSource={data} />
    </Card>
  );
};

export default ProcessConfig;
