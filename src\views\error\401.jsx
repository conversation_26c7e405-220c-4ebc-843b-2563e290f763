import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

const Error401 = () => {
  const navigate = useNavigate();

  return (
    <Result
      status="403"
      title="401"
      subTitle="抱歉，您没有权限访问此页面。"
      extra={
        <Button type="primary" onClick={() => navigate('/login')}>
          去登录
        </Button>
      }
    />
  );
};

export default Error401;
